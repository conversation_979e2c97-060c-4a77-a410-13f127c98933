# config.py

import os
import sys
import json
import logging
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# 定义资源路径函数
def resource_path(relative_path):
    """获取资源文件的绝对路径，兼容开发环境和打包后的 exe"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        base_path = sys._MEIPASS
    else:
        # 未打包的情况，直接使用脚本目录
        base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, relative_path)

def _mask_secret(value: str, show: int = 4) -> str:
    if not value:
        return "<empty>"
    if len(value) <= show:
        return "*" * len(value)
    return f"{'*' * (len(value) - show)}{value[-show:]}"

# 从环境变量加载API密钥（兼容原有默认值），不打印明文
API_KEY = os.getenv("API_KEY", "lxw8025031")
logger.info(f"Loaded API key: {_mask_secret(API_KEY)}")

# Coze/Enterprise credit streaming配置改为从环境变量读取
COZE_API_TOKEN = os.getenv("COZE_API_TOKEN", "")
COZE_WORKFLOW_ID = os.getenv("COZE_WORKFLOW_ID", "")
if COZE_API_TOKEN:
    logger.info("Loaded Coze API token: (hidden)")
if COZE_WORKFLOW_ID:
    logger.info(f"Loaded Coze workflow id: {_mask_secret(COZE_WORKFLOW_ID)}")

def is_valid_api_key(key):
    """检查API密钥是否有效（更严格的验证）"""
    # 检查密钥不为空且不是默认值
    return key is not None and key != "" and key != "sdsdsd"

# Define status color mapping
STATUS_COLOR_MAPPING = {
    '逾期还款': '#FFFFEB9C',   # RGB(255, 235, 156)
    '提前还款': '#FFD9E1F2',   # RGB(217, 225, 242)
    '按时还款': '#FFC6EFCE',   # RGB(198, 239, 206)
    '账单日': '#FFF4B084',     # RGB(244, 176, 132)
    '逾期未还': '#FFFFC7CE',   # RGB(255, 199, 206)
    '无效日期': '#FFDEDEDE',   # RGB(222, 222, 222)
    '未到还款日期': '#FFFFFFFF',  # White
    '催收': '#FFFFEB9C',        # New
    '诉讼': '#FFFFC7CE',        # New
    '未知状态': '#FFC0C0C0'     # Gray
}

# Version number
VERSION = "v2.0"

class Config:
    """基础配置类"""
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev_key_please_change_in_production')
    API_KEY = API_KEY  # 使用上面定义的API密钥
    COZE_API_TOKEN = COZE_API_TOKEN
    COZE_WORKFLOW_ID = COZE_WORKFLOW_ID
    
    # 会话超时配置
    SESSION_LIFETIME_HOURS = 8  # 设置会话超时时间为8小时
    
    # API URL配置
    # 配置多个可能的API URL格式以便尝试
    API_BASE_URL = 'http://115.190.29.10:5000'  # 首选URL，与文档一致
    API_FALLBACK_URLS = [
        'http://115.190.29.10:5000/api',  # 备用1：带api前缀
        'http://115.190.29.10:8088',      # 备用2：可能使用不同端口
        'http://115.190.29.10'            # 备用3：更简洁的路径格式
    ]
    
    # 用户权限
    # 用户权限（支持从环境变量覆盖，JSON格式）
    _default_user_levels = {
        'TT2024': 'limited',   # 有限权限
        '881017': 'standard',  # 标准权限
        'Doolin': 'full'       # 完全权限
    }
    try:
        USER_LEVELS = json.loads(os.getenv('USER_LEVELS_JSON', '')) or _default_user_levels
    except Exception:
        USER_LEVELS = _default_user_levels
    
    # 版本号
    VERSION = VERSION
    
    # 状态颜色映射（用于前端CSS）
    STATUS_COLORS = {
        '逾期还款': '#FFFFEB9C',
        '提前还款': '#FFD9E1F2',
        '按时还款': '#FFC6EFCE',
        '账单日': '#FFF4B084',
        '逾期未还': '#FFFFC7CE',
        '无效日期': '#FFDEDEDE',
        '未到还款日期': '#FFFFFFFF',
        '催收': '#FFFFEB9C',
        '诉讼': '#FFFFC7CE',
        '未知状态': '#FFC0C0C0'
    }
    
    # 缓存配置
    CACHE_TYPE = 'SimpleCache'  # 使用简单内存缓存
    CACHE_DEFAULT_TIMEOUT = 300  # 默认缓存时间：5分钟
    CACHE_THRESHOLD = 500       # 最大缓存条目数
    
    # 不同类型数据的缓存时间设置（秒）
    CACHE_TIMEOUTS = {
        'filter_data': 300,         # 筛选数据：5分钟
        'filter_overdue_orders': 600,  # 逾期订单：10分钟
        'filter_orders_by_customer_name': 600,  # 客户订单：10分钟
        'summary_data': 1800,       # 汇总数据：30分钟
        'order_summary': 1800,      # 订单汇总：30分钟
        'customer_summary': 600     # 客户汇总：10分钟
    }

    @staticmethod
    def is_valid_api_key(key):
        """检查API密钥是否有效"""
        return key is not None and key != "" and key != "sdsdsd"

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    FLASK_ENV = 'development'

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    FLASK_ENV = 'production'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True

# 配置映射
config_by_name = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig
}

# 默认配置
config = config_by_name[os.getenv('FLASK_ENV', 'development')]
