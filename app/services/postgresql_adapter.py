"""PostgreSQL数据库适配器

实现PostgreSQL数据库的串码查重功能。
"""

import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from contextlib import contextmanager
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from .database_adapter import (
    DatabaseAdapter, 
    DatabaseConnectionError, 
    DatabaseOperationError,
    DatabaseConfigurationError
)
from .config_manager import DatabaseConfig

logger = logging.getLogger(__name__)


class PostgreSQLAdapter(DatabaseAdapter):
    """PostgreSQL数据库适配器
    
    实现基于PostgreSQL的串码查重功能。
    """
    
    def __init__(self, config: DatabaseConfig):
        """初始化PostgreSQL适配器
        
        Args:
            config: 数据库配置对象
            
        Raises:
            DatabaseConfigurationError: 配置无效时抛出
        """
        if config.database_type != 'postgresql':
            raise DatabaseConfigurationError(
                f"配置类型错误: 期望postgresql，实际{config.database_type}"
            )
        
        if not config.validate():
            raise DatabaseConfigurationError("PostgreSQL配置验证失败")
        
        self.config = config
        self._connection = None
        self._connected = False
        
        logger.info(f"PostgreSQL适配器已初始化: {config.get_safe_connection_string()}")
    
    def connect(self) -> bool:
        """建立数据库连接
        
        Returns:
            bool: 连接是否成功
            
        Raises:
            DatabaseConnectionError: 连接失败时抛出
        """
        try:
            if self._connected and self._connection:
                return True
            
            self._connection = psycopg2.connect(
                self.config.connection_string,
                cursor_factory=RealDictCursor,
                connect_timeout=self.config.timeout
            )
            self._connected = True
            
            logger.info("PostgreSQL连接已建立")
            return True
            
        except psycopg2.Error as e:
            self._connected = False
            error_msg = f"PostgreSQL连接失败: {e}"
            logger.error(error_msg)
            raise DatabaseConnectionError(error_msg) from e
    
    def disconnect(self) -> None:
        """关闭数据库连接"""
        try:
            if self._connection:
                self._connection.close()
                self._connection = None
            self._connected = False
            logger.info("PostgreSQL连接已关闭")
        except Exception as e:
            logger.warning(f"关闭PostgreSQL连接时出现警告: {e}")
    
    def is_connected(self) -> bool:
        """检查数据库连接状态
        
        Returns:
            bool: 是否已连接
        """
        try:
            if not self._connected or not self._connection:
                return False
            
            # 测试连接是否有效
            with self._connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            return True
        except Exception:
            self._connected = False
            return False
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        if not self.is_connected():
            self.connect()
        
        try:
            yield self._connection
        except Exception as e:
            if self._connection:
                self._connection.rollback()
            raise e
    
    def create_tables(self) -> None:
        """创建数据库表结构
        
        Raises:
            DatabaseOperationError: 创建表失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    # 创建串码表
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS serial_numbers (
                            id SERIAL PRIMARY KEY,
                            serial_number VARCHAR(15) UNIQUE NOT NULL,
                            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            created_by VARCHAR(100),
                            notes TEXT
                        );
                    """)
                    
                    # 添加约束
                    cursor.execute("""
                        DO $$ 
                        BEGIN
                            IF NOT EXISTS (
                                SELECT 1 FROM pg_constraint 
                                WHERE conname = 'chk_serial_number_length'
                            ) THEN
                                ALTER TABLE serial_numbers 
                                ADD CONSTRAINT chk_serial_number_length 
                                CHECK (LENGTH(serial_number) = 15);
                            END IF;
                        END $$;
                    """)
                    
                    cursor.execute("""
                        DO $$ 
                        BEGIN
                            IF NOT EXISTS (
                                SELECT 1 FROM pg_constraint 
                                WHERE conname = 'chk_serial_number_numeric'
                            ) THEN
                                ALTER TABLE serial_numbers 
                                ADD CONSTRAINT chk_serial_number_numeric 
                                CHECK (serial_number ~ '^[0-9]{15}$');
                            END IF;
                        END $$;
                    """)
                    
                    conn.commit()
                    logger.info("PostgreSQL表结构创建完成")
                    
        except psycopg2.Error as e:
            error_msg = f"创建PostgreSQL表失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def create_indexes(self) -> None:
        """创建数据库索引
        
        Raises:
            DatabaseOperationError: 创建索引失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    # 创建索引
                    indexes = [
                        "CREATE INDEX IF NOT EXISTS idx_serial_numbers_serial_number ON serial_numbers(serial_number);",
                        "CREATE INDEX IF NOT EXISTS idx_serial_numbers_created_at ON serial_numbers(created_at);",
                        "CREATE INDEX IF NOT EXISTS idx_serial_numbers_created_by ON serial_numbers(created_by);"
                    ]
                    
                    for index_sql in indexes:
                        cursor.execute(index_sql)
                    
                    conn.commit()
                    logger.info("PostgreSQL索引创建完成")
                    
        except psycopg2.Error as e:
            error_msg = f"创建PostgreSQL索引失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def check_duplicate(self, serial_number: str) -> bool:
        """检查串码是否重复
        
        Args:
            serial_number: 15位串码
            
        Returns:
            bool: True表示已存在（重复），False表示不存在
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        'SELECT COUNT(*) as count FROM serial_numbers WHERE serial_number = %s',
                        (serial_number,)
                    )
                    result = cursor.fetchone()
                    count = result['count']
                    
                    result = count > 0
                    logger.debug(f"串码查重结果: {serial_number} - {'已存在' if result else '不存在'}")
                    return result
                    
        except psycopg2.Error as e:
            error_msg = f"检查串码重复失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def add_serial_number(self, serial_number: str, 
                          created_by: Optional[str] = None, 
                          notes: Optional[str] = None) -> Dict[str, Any]:
        """添加新串码
        
        Args:
            serial_number: 15位串码
            created_by: 创建者
            notes: 备注信息
            
        Returns:
            Dict: 包含操作结果的字典
            
        Raises:
            DatabaseOperationError: 插入失败时抛出
        """
        try:
            # 先检查是否已存在
            if self.check_duplicate(serial_number):
                return {
                    'success': False,
                    'error': '已重复',
                    'message': f'串码 {serial_number} 已存在'
                }
            
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO serial_numbers (serial_number, created_at, created_by, notes)
                        VALUES (%s, %s, %s, %s)
                        RETURNING id
                    """, (
                        serial_number,
                        datetime.now(),
                        created_by,
                        notes
                    ))
                    
                    record_id = cursor.fetchone()['id']
                    conn.commit()
                    
                    logger.info(f"成功添加串码: {serial_number}, ID: {record_id}")
                    
                    return {
                        'success': True,
                        'message': '已录入',
                        'record_id': record_id,
                        'serial_number': serial_number,
                        'created_at': datetime.now().isoformat()
                    }
                    
        except psycopg2.IntegrityError as e:
            # 处理唯一约束违反（并发情况下可能发生）
            logger.warning(f"串码 {serial_number} 违反唯一约束: {e}")
            return {
                'success': False,
                'error': '已重复',
                'message': f'串码 {serial_number} 已存在'
            }
        except psycopg2.Error as e:
            error_msg = f"添加串码失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def get_serial_number_info(self, 
                               serial_number: str) -> Optional[Dict[str, Any]]:
        """获取串码详细信息
        
        Args:
            serial_number: 15位串码
            
        Returns:
            Optional[Dict]: 串码信息，如果不存在则返回None
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT id, serial_number, created_at, created_by, notes
                        FROM serial_numbers 
                        WHERE serial_number = %s
                    """, (serial_number,))
                    
                    row = cursor.fetchone()
                    if row:
                        return {
                            'id': row['id'],
                            'serial_number': row['serial_number'],
                            'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                            'created_by': row['created_by'],
                            'notes': row['notes']
                        }
                    return None
                    
        except psycopg2.Error as e:
            error_msg = f"获取串码信息失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def get_recent_records(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的串码记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List[Dict]: 最近的串码记录列表
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT id, serial_number, created_at, created_by, notes
                        FROM serial_numbers 
                        ORDER BY created_at DESC
                        LIMIT %s
                    """, (limit,))
                    
                    rows = cursor.fetchall()
                    return [
                        {
                            'id': row['id'],
                            'serial_number': row['serial_number'],
                            'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                            'created_by': row['created_by'],
                            'notes': row['notes']
                        }
                        for row in rows
                    ]
                    
        except psycopg2.Error as e:
            error_msg = f"获取最近记录失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取串码统计信息
        
        Returns:
            Dict: 统计信息
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        try:
            now = datetime.now()
            start_today = now.replace(hour=0, minute=0, second=0, microsecond=0)
            start_tomorrow = start_today + timedelta(days=1)
            start_week = start_today + timedelta(days=-6)
            
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    # 总记录数
                    cursor.execute('SELECT COUNT(*) FROM serial_numbers')
                    result = cursor.fetchone()
                    total_count = list(result.values())[0]
                    
                    # 今日新增
                    cursor.execute("""
                        SELECT COUNT(*) FROM serial_numbers
                        WHERE created_at >= %s AND created_at < %s
                    """, (start_today, start_tomorrow))
                    result = cursor.fetchone()
                    today_count = list(result.values())[0]
                    
                    # 本周新增
                    cursor.execute("""
                        SELECT COUNT(*) FROM serial_numbers
                        WHERE created_at >= %s
                    """, (start_week,))
                    result = cursor.fetchone()
                    week_count = list(result.values())[0]
                    
                    return {
                        'total_count': total_count,
                        'today_count': today_count,
                        'week_count': week_count,
                        'last_updated': datetime.now().isoformat()
                    }
                    
        except psycopg2.Error as e:
            error_msg = f"获取统计信息失败: {e}"
            logger.error(error_msg)
            return {
                'total_count': 0,
                'today_count': 0,
                'week_count': 0,
                'last_updated': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def get_record_count(self) -> int:
        """获取总记录数
        
        Returns:
            int: 总记录数
            
        Raises:
            DatabaseOperationError: 查询失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute('SELECT COUNT(*) FROM serial_numbers')
                    result = cursor.fetchone()
                    return result['count'] if 'count' in result else list(result.values())[0]
                    
        except psycopg2.Error as e:
            error_msg = f"获取记录数失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def export_all_data(self) -> List[Dict[str, Any]]:
        """导出所有数据
        
        Returns:
            List[Dict]: 所有串码记录列表
            
        Raises:
            DatabaseOperationError: 导出失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT id, serial_number, created_at, created_by, notes
                        FROM serial_numbers 
                        ORDER BY id
                    """)
                    
                    rows = cursor.fetchall()
                    return [
                        {
                            'id': row['id'],
                            'serial_number': row['serial_number'],
                            'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                            'created_by': row['created_by'],
                            'notes': row['notes']
                        }
                        for row in rows
                    ]
                    
        except psycopg2.Error as e:
            error_msg = f"导出数据失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def import_data(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量导入数据
        
        Args:
            records: 要导入的记录列表
            
        Returns:
            Dict: 导入结果
            
        Raises:
            DatabaseOperationError: 导入失败时抛出
        """
        imported_count = 0
        failed_count = 0
        errors = []
        
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    for record in records:
                        try:
                            cursor.execute("""
                                INSERT INTO serial_numbers (serial_number, created_at, created_by, notes)
                                VALUES (%s, %s, %s, %s)
                                ON CONFLICT (serial_number) DO NOTHING
                            """, (
                                record.get('serial_number'),
                                record.get('created_at'),
                                record.get('created_by'),
                                record.get('notes')
                            ))
                            
                            if cursor.rowcount > 0:
                                imported_count += 1
                            
                        except Exception as e:
                            failed_count += 1
                            errors.append(f"记录 {record.get('serial_number', 'unknown')}: {str(e)}")
                    
                    conn.commit()
                    
                    logger.info(f"批量导入完成: 成功{imported_count}条，失败{failed_count}条")
                    
                    return {
                        'success': True,
                        'imported_count': imported_count,
                        'failed_count': failed_count,
                        'errors': errors
                    }
                    
        except psycopg2.Error as e:
            error_msg = f"批量导入失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def validate_schema(self) -> bool:
        """验证数据库表结构
        
        Returns:
            bool: 表结构是否正确
            
        Raises:
            DatabaseOperationError: 验证失败时抛出
        """
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    # 检查表是否存在
                    cursor.execute("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_name = 'serial_numbers'
                        ) as exists
                    """)

                    result = cursor.fetchone()
                    table_exists = result['exists']
                    if not table_exists:
                        logger.error("表 serial_numbers 不存在")
                        return False
                    
                    # 检查必要的列
                    cursor.execute("""
                        SELECT column_name, data_type 
                        FROM information_schema.columns 
                        WHERE table_name = 'serial_numbers'
                    """)
                    
                    columns = {row['column_name']: row['data_type'] for row in cursor.fetchall()}
                    
                    required_columns = {
                        'id': 'integer',
                        'serial_number': 'character varying',
                        'created_at': 'timestamp with time zone',
                        'created_by': 'character varying',
                        'notes': 'text'
                    }
                    
                    for col_name, expected_type in required_columns.items():
                        if col_name not in columns:
                            logger.error(f"缺少列: {col_name}")
                            return False
                        
                        actual_type = columns[col_name]
                        if expected_type not in actual_type:
                            logger.warning(f"列 {col_name} 类型不匹配: 期望包含 {expected_type}，实际 {actual_type}")
                    
                    logger.info("PostgreSQL表结构验证通过")
                    return True
                    
        except psycopg2.Error as e:
            error_msg = f"验证表结构失败: {e}"
            logger.error(error_msg)
            raise DatabaseOperationError(error_msg) from e
    
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        self.disconnect()