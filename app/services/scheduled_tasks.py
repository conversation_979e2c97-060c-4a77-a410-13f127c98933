"""
定时任务：每天 02:00 可选的缓存预热。
"""
import logging
import datetime
import os
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from flask import current_app

from app.services.data_service import get_filtered_data, process_overdue_data
from app.utils.api_client import make_api_request
from app import cache

logger = logging.getLogger(__name__)


def preload_today_data():
    """轻量预热：预加载今日数据，并一次性获取全部逾期订单并写入缓存。"""
    try:
        today = datetime.date.today().strftime('%Y-%m-%d')
        logger.info(f"开始预热缓存：{today}")

        # 今日待处理订单（仅走缓存/短调用）
        today_orders = get_filtered_data(today)
        cache.set('daily_today_orders_count', len(today_orders.get('results', [])), timeout=86400)
        cache.set('daily_today_orders_data', today_orders, timeout=86400)
        logger.info(f"已缓存今日待处理订单：{len(today_orders.get('results', []))} 条")

        # 逾期订单一次性拉取
        params = {'api_key': current_app.config.get('API_KEY'), 'limit': 100000}
        raw = make_api_request('GET', 'filter_overdue_orders_db', params)
        processed = process_overdue_data(raw) if raw else {'results': [], 'columns': []}
        total = len(processed.get('results', []))
        cache.set('daily_overdue_orders_count', total, timeout=86400)
        cache.set('daily_overdue_orders_data', processed, timeout=86400)
        logger.info(f"已缓存逾期订单：{total} 条")

        logger.info("预热完成；缓存保留 24 小时")
    except Exception as e:
        logger.error(f"预热缓存时出错：{e}", exc_info=True)


def init_scheduler(app):
    """初始化后台定时器（每天 02:00 预热）；可通过 PRELOAD_ON_STARTUP 控制启动时是否预热一次。"""
    with app.app_context():
        scheduler = BackgroundScheduler()
        scheduler.add_job(
            preload_today_data,
            trigger=CronTrigger(hour=2, minute=0),
            id='preload_daily_data',
            replace_existing=True,
            max_instances=1,
        )

        scheduler.start()
        logger.info("调度器已启动；每天 02:00 进行预热")

        if os.getenv('PRELOAD_ON_STARTUP', '0') == '1':
            logger.info('检测到 PRELOAD_ON_STARTUP=1，启动时执行一次预热')
            preload_today_data()

        app.scheduler = scheduler
        return scheduler
