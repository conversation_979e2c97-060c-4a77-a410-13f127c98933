import logging
from typing import Dict, Any

from app import cache
from threading import Thread
from time import time
from flask import current_app, has_app_context
from app.services.scheduled_tasks import preload_today_data

logger = logging.getLogger(__name__)


WARMUP_FLAG_KEY = 'home_stats_warmup_in_progress'
WARMUP_TTL = 300  # seconds


def _maybe_trigger_background_warmup():
    try:
        in_progress = cache.get(WARMUP_FLAG_KEY)
        if in_progress:
            return
        # set flag with TTL to avoid stampede
        cache.set(WARMUP_FLAG_KEY, int(time()), timeout=WARMUP_TTL)

        # capture a real app object from current context
        if not has_app_context():
            # cannot warmup safely without an application context
            return
        app = current_app._get_current_object()

        def _task(app_obj):
            try:
                # push an application context for scheduled_tasks to use
                with app_obj.app_context():
                    preload_today_data()
            finally:
                cache.delete(WARMUP_FLAG_KEY)

        t = Thread(target=_task, args=(app,), daemon=True)
        t.start()
    except Exception:
        # never block on warmup
        pass


def get_home_stats_cached() -> Dict[str, Any]:
    """Return homepage stats from cache only (no upstream calls).

    Adds a 'ready' flag to indicate whether cache has been populated at least once.
    """
    try:
        raw_today = cache.get('daily_today_orders_count')
        raw_overdue = cache.get('daily_overdue_orders_count')

        # Convert to numbers when present; otherwise default to 0
        today_orders = int(raw_today) if isinstance(raw_today, (int, float)) else 0
        overdue_orders = int(raw_overdue) if isinstance(raw_overdue, (int, float)) else 0

        # Fallback: derive from cached datasets if counts are zero
        overdue_ready = (raw_overdue is not None)
        today_ready = (raw_today is not None)

        if overdue_orders == 0:
            try:
                daily_overdue_data = cache.get('daily_overdue_orders_data') or {}
                if isinstance(daily_overdue_data, dict) and 'results' in daily_overdue_data:
                    overdue_orders = max(overdue_orders, len(daily_overdue_data.get('results') or []))
                    overdue_ready = overdue_ready or bool(daily_overdue_data.get('results')) or 'results' in daily_overdue_data
                else:
                    # try full dataset cached by data_service
                    full_overdue = cache.get('overdue_data_full') or {}
                    if isinstance(full_overdue, dict) and 'results' in full_overdue:
                        overdue_orders = max(overdue_orders, len(full_overdue.get('results') or []))
                        overdue_ready = overdue_ready or bool(full_overdue.get('results')) or 'results' in full_overdue
            except Exception:
                pass
        if today_orders == 0:
            try:
                daily_today_data = cache.get('daily_today_orders_data') or {}
                if isinstance(daily_today_data, dict) and 'results' in daily_today_data:
                    today_orders = max(today_orders, len(daily_today_data.get('results') or []))
                    today_ready = today_ready or bool(daily_today_data.get('results')) or 'results' in daily_today_data
            except Exception:
                pass

        # overall readiness: both metrics determined
        ready = bool(overdue_ready and today_ready)

        stats = {
            'today_orders': today_orders,
            'overdue_orders': overdue_orders,
            'system_status': 'normal',
            'api_status': 'online',
            'ready': ready,
            'today_ready': bool(today_ready),
            'overdue_ready': bool(overdue_ready),
        }

        # If cache not ready, trigger background warmup once
        if not ready:
            _maybe_trigger_background_warmup()

        return stats
    except Exception:
        # Defensive default
        return {
            'today_orders': 0,
            'overdue_orders': 0,
            'system_status': 'unknown',
            'api_status': 'unknown',
            'ready': False,
        }
