import logging
import json
import hashlib
from typing import Optional, Dict, Any

import requests
from flask import current_app
from requests.adapters import HTTPAdapter

try:
    from urllib3.util.retry import Retry
except Exception:  # pragma: no cover
    Retry = None


logger = logging.getLogger(__name__)

# 备用API路径配置
FALLBACK_ENDPOINTS = {
    'query': 'filter_data_db',
    'overdue': 'filter_overdue_orders_db',
    'customer': 'filter_orders_by_customer_name_db',
    'summary': 'summary_data_db'
}

# 直接尝试的端点列表
DIRECT_ENDPOINTS = [
    '',
    'filter_data_db',
    'filter_overdue_orders_db',
    'filter_orders_by_customer_name_db'
]

_session: Optional[requests.Session] = None


def _get_session() -> requests.Session:
    global _session
    if _session is not None:
        return _session

    s = requests.Session()
    s.trust_env = False  # ignore system proxies
    if Retry is not None:
        retry = Retry(
            total=3,
            backoff_factor=0.5,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST"],
            raise_on_status=False,
        )
        adapter = HTTPAdapter(max_retries=retry)
        s.mount('http://', adapter)
        s.mount('https://', adapter)
    _session = s
    return _session


class ApiClient:
    """API客户端类，用于处理与后端API的通信"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def get_filter_data(self, start_date: Optional[str], end_date: Optional[str]):
        self.logger.info("获取筛选数据")
        params: Dict[str, Any] = {}
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
        params['_t'] = hashlib.md5(str(current_app.config.get('API_KEY', '')).encode()).hexdigest()
        return make_api_request('GET', 'filter_data_db', params)

    def get_customer_data(self, customer_name: str):
        self.logger.info("获取客户数据")
        params = {
            'customer_name': customer_name,
            '_t': hashlib.md5(str(current_app.config.get('API_KEY', '')).encode()).hexdigest(),
        }
        return make_api_request('GET', 'filter_orders_by_customer_name_db', params)

    def get_overdue_data(self):
        self.logger.info("获取逾期数据")
        params = {'api_key': current_app.config.get('API_KEY')}
        return make_api_request('GET', 'filter_overdue_orders_db', params)

    def check_status(self):
        self.logger.info("检查API状态")
        try:
            base_url = current_app.config.get('SERVER_NAME') or 'localhost:5000'
            if not str(base_url).startswith('http'):
                base_url = f'http://{base_url}'
            ping_url = f'{base_url}/api/ping'
            resp = _get_session().get(ping_url, timeout=5)
            resp.raise_for_status()
            data = resp.json()
            if data.get('status') == 'ok':
                return {'status': 'online', 'message': 'API服务正常'}
            return {'status': 'offline', 'message': 'API服务异常'}
        except Exception as e:
            self.logger.error(f"检查API状态失败: {e}")
            return {'status': 'offline', 'message': f'无法连接API服务: {e}'}


def generate_cache_key(method, endpoint, params, data):
    key_parts = [
        method.upper(),
        endpoint,
        str(sorted(params.items())) if params else "",
        str(data) if data else ""
    ]
    return hashlib.md5("|".join(key_parts).encode()).hexdigest()


def ensure_db_endpoint(endpoint: str) -> str:
    # 如果已经有_db后缀或是特殊端点则不做修改
    if endpoint.endswith('_db') or endpoint in ['', 'ping', 'version', 'status']:
        return endpoint

    endpoint_mapping = {
        'filter_data': 'filter_data_db',
        'filter_overdue_orders': 'filter_overdue_orders_db',
        'filter_orders_by_customer_name': 'filter_orders_by_customer_name_db',
        'customer_summary': 'customer_summary_db',
        'summary_data': 'summary_data_db',
        'order_summary': 'order_summary_db',
        'get_order_details': 'get_order_details_db',
        'delete_order': 'delete_order_db',
        'overdue_summary': 'overdue_summary_db'
    }

    if endpoint in endpoint_mapping:
        logger.info(f"将端点 {endpoint} 映射为 {endpoint_mapping[endpoint]}")
        return endpoint_mapping[endpoint]

    return f"{endpoint}_db"


def make_api_request(method: str,
                     endpoint: str,
                     params: Optional[Dict[str, Any]] = None,
                     data: Optional[Dict[str, Any]] = None,
                     use_fallback: bool = False,
                     url_index: int = 0,
                     direct_endpoint_index: int = 0):
    """发送API请求到原始API服务器（带基础重试与URL回退）。"""

    method = method.upper()
    endpoint = ensure_db_endpoint(endpoint)

    # 选择基础URL
    if url_index == 0:
        base_url = current_app.config.get('API_BASE_URL')
    else:
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        base_url = fallback_urls[url_index - 1] if (url_index - 1) < len(fallback_urls) else None
    if not base_url:
        logger.error("没有可用的API基础URL")
        return {"error": "无法连接到任何可用的API服务"}

    # 端点回退
    if use_fallback and endpoint in FALLBACK_ENDPOINTS:
        actual_endpoint = FALLBACK_ENDPOINTS[endpoint]
    elif 0 < direct_endpoint_index <= len(DIRECT_ENDPOINTS):
        actual_endpoint = DIRECT_ENDPOINTS[direct_endpoint_index - 1]
    else:
        actual_endpoint = endpoint

    url = f"{base_url}/{actual_endpoint.lstrip('/')}"

    # 参数与密钥
    params = dict(params or {})
    api_key = current_app.config.get('API_KEY', '')
    if 'api_key' not in params and api_key:
        params['api_key'] = api_key

    # 轻量日志，避免泄露敏感数据
    try:
        safe_params = {k: ('***' if 'key' in k.lower() else v) for k, v in params.items()}
    except Exception:
        safe_params = {}
    logger.info(f"[URL#{url_index}] {method} {url} params={safe_params}")

    session = _get_session()
    try:
        if method == 'GET':
            resp = session.get(url, params=params, timeout=30)
        elif method == 'POST':
            resp = session.post(url, params=params, json=data, timeout=30)
        else:
            return {"error": f"不支持的请求方法: {method}"}

        status = resp.status_code
        logger.info(f"[URL#{url_index}] 响应状态码: {status}")
        resp.raise_for_status()

        try:
            return resp.json()
        except json.JSONDecodeError:
            text = resp.text
            logger.error("无法解析JSON响应")
            return {"error": "服务器返回了无法解析的响应", "text": text[:200]}

    except requests.exceptions.Timeout as e:
        logger.warning(f"[URL#{url_index}] 请求超时: {e}")
        # 尝试下一个基础URL
        next_index = url_index + 1
        fallback_urls = current_app.config.get('API_FALLBACK_URLS', [])
        if next_index - 1 < len(fallback_urls) or next_index == 1:
            return make_api_request(method, endpoint, params, data, use_fallback, next_index, direct_endpoint_index)
        # 尝试备用端点
        if not use_fallback and endpoint in FALLBACK_ENDPOINTS:
            return make_api_request(method, endpoint, params, data, True, 0, 0)
        return {"error": "服务器响应超时，请稍后再试"}

    except requests.exceptions.RequestException as e:
        logger.error(f"[URL#{url_index}] 请求失败: {e}")
        return {"error": f"API请求失败: {e}"}

